import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/schedule.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    try {
      String path = join(await getDatabasesPath(), 'schedule_app.db');
      return await openDatabase(
        path,
        version: 1,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
      );
    } catch (e) {
      print('Error initializing database: $e');
      rethrow;
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE schedules (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        start_date_time INTEGER NOT NULL,
        end_date_time INTEGER NOT NULL,
        type INTEGER NOT NULL,
        notes TEXT,
        has_reminder INTEGER NOT NULL DEFAULT 0,
        reminder_date_time INTEGER,
        recurrence_type INTEGER NOT NULL DEFAULT 0,
        recurrence_end_date INTEGER,
        color_code TEXT NOT NULL,
        is_completed INTEGER NOT NULL DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Create indexes for better query performance
    await db.execute('''
      CREATE INDEX idx_schedules_start_date ON schedules(start_date_time)
    ''');

    await db.execute('''
      CREATE INDEX idx_schedules_type ON schedules(type)
    ''');

    await db.execute('''
      CREATE INDEX idx_schedules_completed ON schedules(is_completed)
    ''');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < newVersion) {
      // Add migration logic here when needed
    }
  }

  // CRUD Operations

  Future<String> insertSchedule(Schedule schedule) async {
    final db = await database;
    await db.insert(
      'schedules',
      schedule.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    return schedule.id;
  }

  Future<List<Schedule>> getAllSchedules() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'schedules',
      orderBy: 'start_date_time ASC',
    );

    return List.generate(maps.length, (i) {
      return Schedule.fromMap(maps[i]);
    });
  }

  Future<Schedule?> getScheduleById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'schedules',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Schedule.fromMap(maps.first);
    }
    return null;
  }

  Future<List<Schedule>> getSchedulesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'schedules',
      where: 'start_date_time >= ? AND start_date_time <= ?',
      whereArgs: [
        startDate.millisecondsSinceEpoch,
        endDate.millisecondsSinceEpoch,
      ],
      orderBy: 'start_date_time ASC',
    );

    return List.generate(maps.length, (i) {
      return Schedule.fromMap(maps[i]);
    });
  }

  Future<List<Schedule>> getSchedulesByType(ScheduleType type) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'schedules',
      where: 'type = ?',
      whereArgs: [type.index],
      orderBy: 'start_date_time ASC',
    );

    return List.generate(maps.length, (i) {
      return Schedule.fromMap(maps[i]);
    });
  }

  Future<List<Schedule>> searchSchedules(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'schedules',
      where: 'title LIKE ? OR description LIKE ? OR notes LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'start_date_time ASC',
    );

    return List.generate(maps.length, (i) {
      return Schedule.fromMap(maps[i]);
    });
  }

  Future<List<Schedule>> getUpcomingSchedules({int limit = 10}) async {
    final db = await database;
    final now = DateTime.now().millisecondsSinceEpoch;
    final List<Map<String, dynamic>> maps = await db.query(
      'schedules',
      where: 'start_date_time >= ? AND is_completed = 0',
      whereArgs: [now],
      orderBy: 'start_date_time ASC',
      limit: limit,
    );

    return List.generate(maps.length, (i) {
      return Schedule.fromMap(maps[i]);
    });
  }

  Future<List<Schedule>> getTodaySchedules() async {
    final now = DateTime.now();
    final startOfDay = DateTime(now.year, now.month, now.day);
    final endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);

    return await getSchedulesByDateRange(startOfDay, endOfDay);
  }

  Future<int> updateSchedule(Schedule schedule) async {
    final db = await database;
    return await db.update(
      'schedules',
      schedule.toMap(),
      where: 'id = ?',
      whereArgs: [schedule.id],
    );
  }

  Future<int> deleteSchedule(String id) async {
    final db = await database;
    return await db.delete('schedules', where: 'id = ?', whereArgs: [id]);
  }

  Future<int> deleteAllSchedules() async {
    final db = await database;
    return await db.delete('schedules');
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }

  // Utility methods
  Future<int> getScheduleCount() async {
    final db = await database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM schedules');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  Future<List<Schedule>> getSchedulesWithReminders() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'schedules',
      where: 'has_reminder = 1 AND reminder_date_time IS NOT NULL',
      orderBy: 'reminder_date_time ASC',
    );

    return List.generate(maps.length, (i) {
      return Schedule.fromMap(maps[i]);
    });
  }
}
