package com.example.flutter_schedule_augment

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.widget.RemoteViews
import es.antonborri.home_widget.HomeWidgetPlugin
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*
import android.util.Log // Import Log

class TestScheduleWidgetProvider : AppWidgetProvider() {

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    private fun updateAppWidget(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int
    ) {
        val views = RemoteViews(context.packageName, R.layout.schedule_widget_test)
        Log.d("TestScheduleWidgetProvider", "Updating widget ID: $appWidgetId") // Log widget update

        try {
            // Get schedule data and count upcoming events
            val widgetData = HomeWidgetPlugin.getData(context)
            val scheduleData = widgetData.getString("upcoming_schedules", null)
            Log.d("TestScheduleWidgetProvider", "Raw scheduleData: $scheduleData") // Log raw data

            if (scheduleData != null && scheduleData.isNotEmpty()) {
                val schedules = JSONArray(scheduleData)
                val upcomingEvents = getUpcomingEvents(schedules)
                Log.d("TestScheduleWidgetProvider", "Upcoming events count: ${upcomingEvents.size}") // Log upcoming events count

                if (upcomingEvents.isNotEmpty()) {
                    updateWithEvents(views, upcomingEvents)
                    updateEventCount(views, upcomingEvents.size)
                    showContentState(views)
                    Log.d("TestScheduleWidgetProvider", "Showing content state")
                } else {
                    showEmptyState(views)
                    Log.d("TestScheduleWidgetProvider", "Showing empty state because no upcoming events after filtering")
                }
            } else {
                showEmptyState(views)
                Log.d("TestScheduleWidgetProvider", "Showing empty state because scheduleData is null or empty")
            }
        } catch (e: Exception) {
            showEmptyState(views)
            Log.e("TestScheduleWidgetProvider", "Error updating widget: ${e.message}", e) // Log exception
        }

        // Set up click intents
        setupClickIntents(views, context)

        appWidgetManager.updateAppWidget(appWidgetId, views)
        Log.d("TestScheduleWidgetProvider", "Widget ID: $appWidgetId update complete")
    }
    
    private fun getUpcomingEvents(schedules: JSONArray): List<JSONObject> {
        val now = System.currentTimeMillis()
        val upcomingEvents = mutableListOf<JSONObject>()

        for (i in 0 until schedules.length()) {
            try {
                val schedule = schedules.getJSONObject(i)
                val startTime = schedule.getLong("startDateTime")
                val isCompleted = schedule.getBoolean("isCompleted")

                // Only include future, uncompleted events
                if (startTime > now && !isCompleted) {
                    upcomingEvents.add(schedule)
                    if (upcomingEvents.size >= 30) break // Limit to 30 for widget display
                }
            } catch (e: Exception) {
                // Skip invalid entries
            }
        }

        return upcomingEvents
    }

    private fun updateWithEvents(views: RemoteViews, events: List<JSONObject>) {
        // Define available event item IDs (currently we have 8 items in layout)
        val eventIds = arrayOf(
            R.id.event_item_1, R.id.event_item_2, R.id.event_item_3,
            R.id.event_item_4, R.id.event_item_5, R.id.event_item_6,
            R.id.event_item_7, R.id.event_item_8
        )

        // Hide all event items first
        for (id in eventIds) {
            try {
                views.setViewVisibility(id, android.view.View.GONE)
            } catch (e: Exception) {
                // Ignore if view doesn't exist
            }
        }

        // Show and populate visible items (limit to available slots)
        val maxEvents = minOf(events.size, eventIds.size)
        Log.d("TestScheduleWidgetProvider", "Displaying $maxEvents events out of ${events.size} total events")

        for (i in 0 until maxEvents) {
            try {
                Log.d("TestScheduleWidgetProvider", "Processing event $i: ${events[i].getString("title")}")
                updateEventItem(views, events[i], i + 1)
                views.setViewVisibility(eventIds[i], android.view.View.VISIBLE)
                Log.d("TestScheduleWidgetProvider", "Made event item ${i + 1} visible with ID ${eventIds[i]}")
            } catch (e: Exception) {
                Log.e("TestScheduleWidgetProvider", "Error updating event item ${i + 1}: ${e.message}")
            }
        }

        // Show "more events" indicator only if there are more than available slots
        if (events.size > eventIds.size) {
            val remainingCount = events.size - eventIds.size
            views.setTextViewText(R.id.more_events_text, "+ $remainingCount more events")
            views.setViewVisibility(R.id.more_events_text, android.view.View.VISIBLE)
        } else {
            views.setViewVisibility(R.id.more_events_text, android.view.View.GONE)
        }
    }

    private fun updateEventItem(views: RemoteViews, schedule: JSONObject, itemNumber: Int) {
        try {
            val title = schedule.getString("title")
            val description = schedule.getString("description")
            val startTime = schedule.getLong("startDateTime")
            val type = schedule.getInt("type")
            val colorCode = schedule.getString("colorCode")

            Log.d("TestScheduleWidgetProvider", "Updating event item $itemNumber: title='$title', time=$startTime")

            // Format time and date
            val date = Date(startTime)
            val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
            val formattedTime = timeFormat.format(date)
            val dateText = getDateText(date)

            // Get view IDs based on item number
            val timeId = getResourceId("item_time", itemNumber)
            val dateId = getResourceId("item_date", itemNumber)
            val titleId = getResourceId("item_title", itemNumber)
            val descId = getResourceId("item_description", itemNumber)
            val typeId = getResourceId("item_type", itemNumber)
            val indicatorId = getResourceId("item_indicator", itemNumber)

            Log.d("TestScheduleWidgetProvider", "Resource IDs for item $itemNumber: timeId=$timeId, titleId=$titleId")

            // Update views
            views.setTextViewText(timeId, formattedTime)
            views.setTextViewText(dateId, dateText)
            views.setTextViewText(titleId, title)
            views.setTextViewText(typeId, getTypeName(type))

            // Show description if not empty and different from title
            if (description.isNotEmpty() && description != title) {
                views.setTextViewText(descId, description)
                views.setViewVisibility(descId, android.view.View.VISIBLE)
            } else {
                views.setViewVisibility(descId, android.view.View.GONE)
            }

            // Set color indicator - use special color for today's events
            val isToday = isSameDay(Calendar.getInstance().apply { time = Date() },
                                  Calendar.getInstance().apply { time = date })
            val color = if (isToday) {
                getTodayEventColor()
            } else {
                parseColor(colorCode, type)
            }
            views.setInt(indicatorId, "setBackgroundColor", color)

            // Apply today's styling to the event item background
            if (isToday) {
                applyTodayEventStyling(views, itemNumber)
            } else {
                applyNormalEventStyling(views, itemNumber)
            }

        } catch (e: Exception) {
            // Handle error silently
        }
    }

    private fun getResourceId(baseName: String, itemNumber: Int): Int {
        return when (baseName) {
            "item_time" -> when (itemNumber) {
                1 -> R.id.event_1_time
                2 -> R.id.event_2_time
                3 -> R.id.event_3_time
                4 -> R.id.event_4_time
                5 -> R.id.event_5_time
                6 -> R.id.event_6_time
                7 -> R.id.event_7_time
                8 -> R.id.event_8_time
                else -> R.id.event_1_time
            }
            "item_date" -> when (itemNumber) {
                1 -> R.id.event_1_date
                2 -> R.id.event_2_date
                3 -> R.id.event_3_date
                4 -> R.id.event_4_date
                5 -> R.id.event_5_date
                6 -> R.id.event_6_date
                7 -> R.id.event_7_date
                8 -> R.id.event_8_date
                else -> R.id.event_1_date
            }
            "item_title" -> when (itemNumber) {
                1 -> R.id.event_1_title
                2 -> R.id.event_2_title
                3 -> R.id.event_3_title
                4 -> R.id.event_4_title
                5 -> R.id.event_5_title
                6 -> R.id.event_6_title
                7 -> R.id.event_7_title
                8 -> R.id.event_8_title
                else -> R.id.event_1_title
            }
            "item_description" -> when (itemNumber) {
                1 -> R.id.event_1_description
                2 -> R.id.event_2_description
                3 -> R.id.event_3_description
                4 -> R.id.event_4_description
                5 -> R.id.event_5_description
                6 -> R.id.event_6_description
                7 -> R.id.event_7_description
                8 -> R.id.event_8_description
                else -> R.id.event_1_description
            }
            "item_type" -> when (itemNumber) {
                1 -> R.id.event_1_type
                2 -> R.id.event_2_type
                3 -> R.id.event_3_type
                4 -> R.id.event_4_type
                5 -> R.id.event_5_type
                6 -> R.id.event_6_type
                7 -> R.id.event_7_type
                8 -> R.id.event_8_type
                else -> R.id.event_1_type
            }
            "item_indicator" -> when (itemNumber) {
                1 -> R.id.event_1_indicator
                2 -> R.id.event_2_indicator
                3 -> R.id.event_3_indicator
                4 -> R.id.event_4_indicator
                5 -> R.id.event_5_indicator
                6 -> R.id.event_6_indicator
                7 -> R.id.event_7_indicator
                8 -> R.id.event_8_indicator
                else -> R.id.event_1_indicator
            }
            else -> R.id.event_1_title // fallback
        }
    }

    private fun getDateText(date: Date): String {
        val now = Date()
        val today = Calendar.getInstance().apply { time = now }
        val scheduleDate = Calendar.getInstance().apply { time = date }

        return when {
            isSameDay(today, scheduleDate) -> "Today"
            isTomorrow(today, scheduleDate) -> "Tomorrow"
            isThisWeek(today, scheduleDate) -> SimpleDateFormat("EEE", Locale.getDefault()).format(date)
            else -> SimpleDateFormat("MMM dd, yyyy", Locale.getDefault()).format(date) // Include year
        }
    }

    private fun isSameDay(cal1: Calendar, cal2: Calendar): Boolean {
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
               cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
    }

    private fun isTomorrow(today: Calendar, date: Calendar): Boolean {
        val tomorrow = today.clone() as Calendar
        tomorrow.add(Calendar.DAY_OF_YEAR, 1)
        return isSameDay(tomorrow, date)
    }

    private fun isThisWeek(today: Calendar, date: Calendar): Boolean {
        val weekStart = today.clone() as Calendar
        weekStart.set(Calendar.DAY_OF_WEEK, weekStart.firstDayOfWeek)

        val weekEnd = weekStart.clone() as Calendar
        weekEnd.add(Calendar.WEEK_OF_YEAR, 1)

        return date.after(weekStart) && date.before(weekEnd)
    }

    private fun parseColor(colorCode: String, type: Int): Int {
        return try {
            android.graphics.Color.parseColor(colorCode)
        } catch (e: Exception) {
            getTypeColorInt(type)
        }
    }

    private fun getTypeColorInt(type: Int): Int {
        return when (type) {
            0 -> 0xFFFF6B6B.toInt() // Birthday
            1 -> 0xFFFF8E8E.toInt() // Anniversary
            2 -> 0xFF4ECDC4.toInt() // Holiday
            3 -> 0xFF45B7D1.toInt() // Personal
            4 -> 0xFF96CEB4.toInt() // Work
            5 -> 0xFFFFEAA7.toInt() // Meeting
            6 -> 0xFFDDA0DD.toInt() // Appointment
            7 -> 0xFFFFB347.toInt() // Reminder
            else -> 0xFFA8A8A8.toInt() // Other
        }
    }

    private fun getTodayEventColor(): Int {
        // Special color for today's events - bright orange/red to make them stand out
        return 0xFFFF4500.toInt() // OrangeRed color
    }

    private fun applyTodayEventStyling(views: RemoteViews, itemNumber: Int) {
        try {
            // Get the event item container ID
            val eventItemId = when (itemNumber) {
                1 -> R.id.event_item_1
                2 -> R.id.event_item_2
                3 -> R.id.event_item_3
                4 -> R.id.event_item_4
                5 -> R.id.event_item_5
                6 -> R.id.event_item_6
                7 -> R.id.event_item_7
                8 -> R.id.event_item_8
                else -> R.id.event_item_1
            }

            // Apply today's background color (light orange tint)
            views.setInt(eventItemId, "setBackgroundColor", 0xFFFFF4E6.toInt())

            // Make title text bold and slightly larger for today's events
            val titleId = getResourceId("item_title", itemNumber)
            views.setTextColor(titleId, 0xFF2E2E2E.toInt()) // Darker text for better contrast

        } catch (e: Exception) {
            // Handle error silently
        }
    }

    private fun applyNormalEventStyling(views: RemoteViews, itemNumber: Int) {
        try {
            // Get the event item container ID
            val eventItemId = when (itemNumber) {
                1 -> R.id.event_item_1
                2 -> R.id.event_item_2
                3 -> R.id.event_item_3
                4 -> R.id.event_item_4
                5 -> R.id.event_item_5
                6 -> R.id.event_item_6
                7 -> R.id.event_item_7
                8 -> R.id.event_item_8
                else -> R.id.event_item_1
            }

            // Apply normal background color
            views.setInt(eventItemId, "setBackgroundColor", 0xFFF8F8F8.toInt())

            // Normal title text color
            val titleId = getResourceId("item_title", itemNumber)
            views.setTextColor(titleId, 0xFF212121.toInt()) // Normal text color

        } catch (e: Exception) {
            // Handle error silently
        }
    }

    private fun updateEventCount(views: RemoteViews, count: Int) {
        val countText = when {
            count == 1 -> "1 event"
            count >= 30 -> "30+ events"
            else -> "$count events"
        }
        views.setTextViewText(R.id.event_count, countText)
    }

    private fun showContentState(views: RemoteViews) {
        views.setViewVisibility(R.id.events_container, android.view.View.VISIBLE)
        views.setViewVisibility(R.id.event_count, android.view.View.VISIBLE)
        views.setViewVisibility(R.id.test_empty, android.view.View.GONE)
    }

    private fun showEmptyState(views: RemoteViews) {
        views.setViewVisibility(R.id.events_container, android.view.View.GONE)
        views.setViewVisibility(R.id.event_count, android.view.View.GONE)
        views.setViewVisibility(R.id.test_empty, android.view.View.VISIBLE)
    }

    private fun setupClickIntents(views: RemoteViews, context: Context) {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Set click intent on header and empty state
        views.setOnClickPendingIntent(R.id.test_empty, pendingIntent)
    }
    
    private fun getTypeName(type: Int): String {
        return when (type) {
            0 -> "Birthday"
            1 -> "Anniversary"
            2 -> "Holiday"
            3 -> "Personal"
            4 -> "Work"
            5 -> "Meeting"
            6 -> "Appointment"
            7 -> "Reminder"
            else -> "Other"
        }
    }
}

