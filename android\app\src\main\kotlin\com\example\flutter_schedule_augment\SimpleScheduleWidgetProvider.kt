package com.example.flutter_schedule_augment

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews
import es.antonborri.home_widget.HomeWidgetPlugin
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

class SimpleScheduleWidgetProvider : AppWidgetProvider() {

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    private fun updateAppWidget(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int
    ) {
        val views = RemoteViews(context.packageName, R.layout.schedule_widget_simple)
        
        try {
            // Get schedule data from shared preferences
            val widgetData = HomeWidgetPlugin.getData(context)
            val scheduleData = widgetData.getString("upcoming_schedules", null)
            
            if (scheduleData != null && scheduleData.isNotEmpty()) {
                val schedules = JSONArray(scheduleData)
                if (schedules.length() > 0) {
                    updateWithScheduleData(views, schedules)
                } else {
                    showEmptyState(views)
                }
            } else {
                showEmptyState(views)
            }
        } catch (e: Exception) {
            showEmptyState(views)
        }
        
        // Set up click intent to open the app
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(R.id.widget_content, pendingIntent)
        
        appWidgetManager.updateAppWidget(appWidgetId, views)
    }
    
    private fun updateWithScheduleData(views: RemoteViews, schedules: JSONArray) {
        try {
            // Show content, hide empty state
            views.setViewVisibility(R.id.widget_content, android.view.View.VISIBLE)
            views.setViewVisibility(R.id.empty_message, android.view.View.GONE)

            // Update first event
            if (schedules.length() > 0) {
                updateEventItem(views, schedules.getJSONObject(0), 1)
                views.setViewVisibility(R.id.event_1, android.view.View.VISIBLE)
            } else {
                views.setViewVisibility(R.id.event_1, android.view.View.GONE)
            }

            // Update second event
            if (schedules.length() > 1) {
                updateEventItem(views, schedules.getJSONObject(1), 2)
                views.setViewVisibility(R.id.event_2, android.view.View.VISIBLE)
            } else {
                views.setViewVisibility(R.id.event_2, android.view.View.GONE)
            }

            // Show more events indicator only if there are more than 30
            if (schedules.length() > 30) {
                val remainingCount = schedules.length() - 30
                views.setTextViewText(R.id.more_events_indicator, "+ $remainingCount more events")
                views.setViewVisibility(R.id.more_events_indicator, android.view.View.VISIBLE)
            } else {
                views.setViewVisibility(R.id.more_events_indicator, android.view.View.GONE)
            }

        } catch (e: Exception) {
            showEmptyState(views)
        }
    }

    private fun updateEventItem(views: RemoteViews, schedule: JSONObject, eventNumber: Int) {
        try {
            val title = schedule.getString("title")
            val startTime = schedule.getLong("startDateTime")
            val type = schedule.getInt("type")
            val colorCode = schedule.getString("colorCode")
            
            // Format time and date
            val date = Date(startTime)
            val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
            val formattedTime = timeFormat.format(date)
            
            // Include year in date format for recurring events
            val now = Calendar.getInstance()
            val scheduleDate = Calendar.getInstance().apply { time = date }
            val dateText = when {
                isSameDay(now, scheduleDate) -> "Today"
                isTomorrow(now, scheduleDate) -> "Tomorrow"
                else -> SimpleDateFormat("MMM dd, yyyy", Locale.getDefault()).format(date) // Include year
            }
            
            // Get view IDs based on event number
            val timeId = if (eventNumber == 1) R.id.event_1_time else R.id.event_2_time
            val titleId = if (eventNumber == 1) R.id.event_1_title else R.id.event_2_title
            val typeId = if (eventNumber == 1) R.id.event_1_type else R.id.event_2_type
            val indicatorId = if (eventNumber == 1) R.id.event_1_indicator else R.id.event_2_indicator
            
            // Update views
            views.setTextViewText(timeId, "$formattedTime ($dateText)")  // Include date with year
            views.setTextViewText(titleId, title)
            views.setTextViewText(typeId, getTypeName(type))
            
            // Set color indicator
            val color = parseColor(colorCode, type)
            views.setInt(indicatorId, "setBackgroundColor", color)
            
        } catch (e: Exception) {
            // Handle error silently
        }
    }

    private fun parseColor(colorCode: String, type: Int): Int {
        return try {
            android.graphics.Color.parseColor(colorCode)
        } catch (e: Exception) {
            // Fallback to type-based color
            getTypeColorInt(type)
        }
    }

    private fun getTypeColorInt(type: Int): Int {
        return when (type) {
            0 -> 0xFFFF6B6B.toInt() // Birthday
            1 -> 0xFFFF8E8E.toInt() // Anniversary
            2 -> 0xFF4ECDC4.toInt() // Holiday
            3 -> 0xFF45B7D1.toInt() // Personal
            4 -> 0xFF96CEB4.toInt() // Work
            5 -> 0xFFFFEAA7.toInt() // Meeting
            6 -> 0xFFDDA0DD.toInt() // Appointment
            7 -> 0xFFFFB347.toInt() // Reminder
            else -> 0xFFA8A8A8.toInt() // Other
        }
    }
    
    private fun showEmptyState(views: RemoteViews) {
        views.setViewVisibility(R.id.widget_content, android.view.View.GONE)
        views.setViewVisibility(R.id.empty_message, android.view.View.VISIBLE)
    }
    
    private fun getTypeName(type: Int): String {
        return when (type) {
            0 -> "Birthday"
            1 -> "Anniversary"
            2 -> "Holiday"
            3 -> "Personal"
            4 -> "Work"
            5 -> "Meeting"
            6 -> "Appointment"
            7 -> "Reminder"
            else -> "Other"
        }
    }

    private fun isSameDay(cal1: Calendar, cal2: Calendar): Boolean {
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
               cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
    }

    private fun isTomorrow(today: Calendar, date: Calendar): Boolean {
        val tomorrow = Calendar.getInstance().apply {
            time = today.time
            add(Calendar.DAY_OF_YEAR, 1)
        }
        return isSameDay(tomorrow, date)
    }
}

