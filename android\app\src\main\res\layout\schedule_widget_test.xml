<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="8dp"
    android:background="#FFFFFF">

    <!-- Widget Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Upcoming Events"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="#212121" />

        <TextView
            android:id="@+id/event_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="5 events"
            android:textSize="10sp"
            android:textColor="#757575" />

    </LinearLayout>

    <!-- Events Container -->
    <LinearLayout
        android:id="@+id/events_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Event 1 -->
        <LinearLayout
            android:id="@+id/event_item_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="4dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="45dp">

                <TextView
                    android:id="@+id/event_1_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_1_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_1_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#45B7D1" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_1_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Meeting with team"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_1_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Weekly team sync"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_1_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Work"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 2 -->
        <LinearLayout
            android:id="@+id/event_item_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="4dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="45dp">

                <TextView
                    android:id="@+id/event_2_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="14:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_2_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_2_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#FF6B6B" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_2_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Doctor appointment"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_2_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Annual checkup"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_2_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Personal"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 3 -->
        <LinearLayout
            android:id="@+id/event_item_3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="4dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="45dp">

                <TextView
                    android:id="@+id/event_3_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="16:30"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_3_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tomorrow"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_3_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#4ECDC4" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_3_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Birthday party"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_3_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="John's birthday"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_3_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Personal"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 4 -->
        <LinearLayout
            android:id="@+id/event_item_4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="4dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="45dp">

                <TextView
                    android:id="@+id/event_4_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="18:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_4_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tomorrow"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_4_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#FFD93D" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_4_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 4"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_4_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 4"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_4_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Work"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 5 -->
        <LinearLayout
            android:id="@+id/event_item_5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="4dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="45dp">

                <TextView
                    android:id="@+id/event_5_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="20:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_5_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tomorrow"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_5_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#6BCF7F" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_5_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 5"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_5_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 5"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_5_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Personal"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 6 -->
        <LinearLayout
            android:id="@+id/event_item_6"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="4dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="45dp">

                <TextView
                    android:id="@+id/event_6_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_6_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_6_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#FF8A80" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_6_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 6"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_6_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 6"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_6_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 7 -->
        <LinearLayout
            android:id="@+id/event_item_7"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="4dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="45dp">

                <TextView
                    android:id="@+id/event_7_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_7_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_7_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#81C784" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_7_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 7"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_7_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 7"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_7_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 8 -->
        <LinearLayout
            android:id="@+id/event_item_8"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="4dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="45dp">

                <TextView
                    android:id="@+id/event_8_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_8_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_8_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#64B5F6" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_8_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 8"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_8_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 8"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_8_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- More events indicator -->
        <TextView
            android:id="@+id/more_events_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="+ 25 more events"
            android:textSize="10sp"
            android:textColor="#757575"
            android:gravity="center"
            android:padding="8dp"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Empty State -->
    <TextView
        android:id="@+id/test_empty"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:text="No upcoming events\nTap to add new events"
        android:textSize="12sp"
        android:textColor="#757575"
        android:gravity="center"
        android:visibility="gone" />

</LinearLayout>
