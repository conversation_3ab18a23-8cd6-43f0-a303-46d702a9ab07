#!/usr/bin/env python3
"""
Script to generate additional event items for the widget layout
"""

def generate_event_item(event_num):
    """Generate XML for a single event item"""
    colors = [
        "#45B7D1", "#FF6B6B", "#4ECDC4", "#FFD93D", "#6BCF7F",
        "#FF8A80", "#81C784", "#64B5F6", "#FFB74D", "#F06292",
        "#9575CD", "#4DB6AC", "#AED581", "#FFD54F", "#FF8A65",
        "#A1887F", "#90A4AE", "#BCAAA4", "#B39DDB", "#80CBC4",
        "#C5E1A5", "#FFCC02", "#FFAB91", "#CE93D8", "#80DEEA"
    ]
    
    color = colors[(event_num - 1) % len(colors)]
    
    return f"""        <!-- Event {event_num} -->
        <LinearLayout
            android:id="@+id/event_item_{event_num}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_{event_num}_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_{event_num}_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_{event_num}_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="{color}" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_{event_num}_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event {event_num}"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_{event_num}_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description {event_num}"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_{event_num}_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

"""

def main():
    """Generate all event items from 6 to 30"""
    print("Generating event items 6-30...")
    
    all_events = ""
    for i in range(6, 31):
        all_events += generate_event_item(i)
    
    print(all_events)
    
    # Save to file
    with open("additional_events.xml", "w") as f:
        f.write(all_events)
    
    print(f"Generated {25} additional event items and saved to additional_events.xml")

if __name__ == "__main__":
    main()
