package com.example.flutter_schedule_augment

import android.os.Bundle
import android.util.Log
import io.flutter.embedding.android.FlutterActivity

class MainActivity : FlutterActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        try {
            super.onCreate(savedInstanceState)
            Log.d("MainActivity", "MainActivity created successfully")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error creating MainActivity", e)
            throw e
        }
    }

    override fun onResume() {
        try {
            super.onResume()
            Log.d("MainActivity", "MainActivity resumed successfully")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error resuming MainActivity", e)
        }
    }
}
