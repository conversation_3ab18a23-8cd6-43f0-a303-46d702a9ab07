        <!-- Event 6 -->
        <LinearLayout
            android:id="@+id/event_item_6"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_6_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_6_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_6_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#FF8A80" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_6_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 6"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_6_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 6"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_6_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 7 -->
        <LinearLayout
            android:id="@+id/event_item_7"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_7_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_7_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_7_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#81C784" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_7_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 7"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_7_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 7"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_7_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 8 -->
        <LinearLayout
            android:id="@+id/event_item_8"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_8_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_8_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_8_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#64B5F6" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_8_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 8"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_8_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 8"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_8_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 9 -->
        <LinearLayout
            android:id="@+id/event_item_9"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_9_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_9_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_9_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#FFB74D" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_9_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 9"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_9_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 9"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_9_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 10 -->
        <LinearLayout
            android:id="@+id/event_item_10"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_10_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_10_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_10_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#F06292" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_10_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 10"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_10_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 10"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_10_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 11 -->
        <LinearLayout
            android:id="@+id/event_item_11"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_11_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_11_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_11_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#9575CD" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_11_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 11"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_11_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 11"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_11_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 12 -->
        <LinearLayout
            android:id="@+id/event_item_12"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_12_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_12_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_12_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#4DB6AC" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_12_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 12"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_12_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 12"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_12_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 13 -->
        <LinearLayout
            android:id="@+id/event_item_13"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_13_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_13_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_13_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#AED581" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_13_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 13"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_13_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 13"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_13_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 14 -->
        <LinearLayout
            android:id="@+id/event_item_14"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_14_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_14_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_14_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#FFD54F" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_14_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 14"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_14_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 14"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_14_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 15 -->
        <LinearLayout
            android:id="@+id/event_item_15"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_15_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_15_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_15_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#FF8A65" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_15_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 15"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_15_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 15"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_15_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 16 -->
        <LinearLayout
            android:id="@+id/event_item_16"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_16_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_16_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_16_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#A1887F" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_16_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 16"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_16_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 16"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_16_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 17 -->
        <LinearLayout
            android:id="@+id/event_item_17"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_17_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_17_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_17_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#90A4AE" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_17_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 17"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_17_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 17"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_17_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 18 -->
        <LinearLayout
            android:id="@+id/event_item_18"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_18_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_18_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_18_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#BCAAA4" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_18_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 18"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_18_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 18"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_18_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 19 -->
        <LinearLayout
            android:id="@+id/event_item_19"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_19_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_19_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_19_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#B39DDB" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_19_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 19"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_19_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 19"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_19_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 20 -->
        <LinearLayout
            android:id="@+id/event_item_20"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_20_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_20_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_20_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#80CBC4" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_20_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 20"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_20_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 20"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_20_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 21 -->
        <LinearLayout
            android:id="@+id/event_item_21"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_21_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_21_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_21_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#C5E1A5" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_21_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 21"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_21_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 21"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_21_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 22 -->
        <LinearLayout
            android:id="@+id/event_item_22"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_22_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_22_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_22_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#FFCC02" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_22_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 22"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_22_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 22"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_22_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 23 -->
        <LinearLayout
            android:id="@+id/event_item_23"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_23_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_23_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_23_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#FFAB91" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_23_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 23"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_23_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 23"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_23_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 24 -->
        <LinearLayout
            android:id="@+id/event_item_24"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_24_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_24_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_24_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#CE93D8" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_24_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 24"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_24_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 24"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_24_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 25 -->
        <LinearLayout
            android:id="@+id/event_item_25"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_25_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_25_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_25_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#80DEEA" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_25_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 25"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_25_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 25"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_25_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 26 -->
        <LinearLayout
            android:id="@+id/event_item_26"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_26_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_26_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_26_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#45B7D1" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_26_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 26"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_26_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 26"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_26_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 27 -->
        <LinearLayout
            android:id="@+id/event_item_27"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_27_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_27_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_27_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#FF6B6B" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_27_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 27"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_27_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 27"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_27_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 28 -->
        <LinearLayout
            android:id="@+id/event_item_28"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_28_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_28_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_28_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#4ECDC4" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_28_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 28"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_28_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 28"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_28_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 29 -->
        <LinearLayout
            android:id="@+id/event_item_29"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_29_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_29_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_29_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#FFD93D" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_29_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 29"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_29_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 29"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_29_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Event 30 -->
        <LinearLayout
            android:id="@+id/event_item_30"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="6dp"
            android:gravity="center_vertical"
            android:background="#F8F8F8"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:minWidth="50dp">

                <TextView
                    android:id="@+id/event_30_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:00"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#212121" />

                <TextView
                    android:id="@+id/event_30_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today"
                    android:textSize="8sp"
                    android:textColor="#757575" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_30_indicator"
                android:layout_width="4dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#6BCF7F" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/event_30_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event 30"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/event_30_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description 30"
                    android:textSize="9sp"
                    android:textColor="#757575"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/event_30_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Event"
                android:textSize="8sp"
                android:textColor="#757575"
                android:background="#F0F0F0"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

